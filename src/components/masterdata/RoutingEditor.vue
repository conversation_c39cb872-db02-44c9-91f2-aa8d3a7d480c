<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { VueFlow, useVueFlow, Panel, PanelPosition } from '@vue-flow/core';
import type { Node, Edge, Connection } from '@vue-flow/core';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { 
  Plus, Save, Undo, Redo, Trash2, Settings, 
  GitBranch, Package, ArrowRight, Warehouse 
} from 'lucide-vue-next';
import { toast } from 'vue-sonner';

// Props
interface Props {
  routingId?: string;
  readonly?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  readonly: false
});

// Emits
const emit = defineEmits<{
  save: [data: { nodes: Node[], edges: Edge[] }];
  cancel: [];
}>();

// Vue Flow instance
const { onConnect, addEdges, removeNodes, removeEdges, findNode, findEdge } = useVueFlow();

// Reactive state
const nodes = ref<Node[]>([]);
const edges = ref<Edge[]>([]);
const selectedNodeId = ref<string | null>(null);
const showNodePanel = ref(false);
const routingVersion = ref('1.0');
const routingStatus = ref<'draft' | 'active' | 'archived'>('draft');

// Node types for the palette
const nodeTypes = [
  {
    type: 'process-segment',
    label: '工艺段',
    icon: GitBranch,
    color: 'bg-blue-500',
    description: '工艺段节点'
  },
  {
    type: 'wip-warehouse',
    label: 'WIP仓库',
    icon: Warehouse,
    color: 'bg-green-500',
    description: '在制品仓库'
  },
  {
    type: 'decision-point',
    label: '决策点',
    icon: ArrowRight,
    color: 'bg-yellow-500',
    description: '路线决策点'
  }
];

// Status options
const statusOptions = [
  { value: 'draft', label: '草稿', variant: 'secondary' },
  { value: 'active', label: '激活', variant: 'default' },
  { value: 'archived', label: '归档', variant: 'outline' }
] as const;

// Computed
const selectedNode = computed(() => {
  return selectedNodeId.value ? findNode(selectedNodeId.value) : null;
});

const canUndo = ref(false);
const canRedo = ref(false);

// Methods
const generateNodeId = () => {
  return `routing_node_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

const generateEdgeId = () => {
  return `routing_edge_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

const addNode = (nodeType: string) => {
  const newNode: Node = {
    id: generateNodeId(),
    type: nodeType,
    position: { x: Math.random() * 400 + 100, y: Math.random() * 300 + 100 },
    data: {
      label: getNodeLabel(nodeType),
      type: nodeType,
      properties: getDefaultNodeProperties(nodeType)
    }
  };

  nodes.value.push(newNode);
  toast.success(`已添加${getNodeTypeLabel(nodeType)}`);
};

const getNodeLabel = (nodeType: string): string => {
  switch (nodeType) {
    case 'process-segment':
      return '新工艺段';
    case 'wip-warehouse':
      return '新WIP仓库';
    case 'decision-point':
      return '决策点';
    default:
      return '新节点';
  }
};

const getNodeTypeLabel = (nodeType: string): string => {
  const type = nodeTypes.find(t => t.type === nodeType);
  return type?.label || nodeType;
};

const getDefaultNodeProperties = (nodeType: string) => {
  switch (nodeType) {
    case 'process-segment':
      return {
        segmentId: '',
        leadTime: 0,
        capacity: 0,
        description: ''
      };
    case 'wip-warehouse':
      return {
        warehouseId: '',
        capacity: 1000,
        unit: 'piece',
        description: ''
      };
    case 'decision-point':
      return {
        condition: '',
        rules: [],
        description: ''
      };
    default:
      return {};
  }
};

const onNodeClick = (event: any) => {
  selectedNodeId.value = event.node.id;
  showNodePanel.value = true;
};

const onPaneClick = () => {
  selectedNodeId.value = null;
  showNodePanel.value = false;
};

const deleteSelectedNode = () => {
  if (selectedNodeId.value) {
    removeNodes([selectedNodeId.value]);
    selectedNodeId.value = null;
    showNodePanel.value = false;
    toast.success('节点已删除');
  }
};

const onConnectHandler = (connection: Connection) => {
  const edge: Edge = {
    id: generateEdgeId(),
    source: connection.source,
    target: connection.target,
    sourceHandle: connection.sourceHandle,
    targetHandle: connection.targetHandle,
    type: 'default',
    data: {
      label: '',
      condition: '',
      leadTime: 0
    }
  };
  
  addEdges([edge]);
  toast.success('连接已创建');
};

const saveRouting = () => {
  const data = {
    nodes: nodes.value,
    edges: edges.value,
    version: routingVersion.value,
    status: routingStatus.value
  };
  
  emit('save', data);
  toast.success('工艺路线已保存');
};

const clearAll = () => {
  if (confirm('确定要清空所有节点和连接吗？')) {
    nodes.value = [];
    edges.value = [];
    selectedNodeId.value = null;
    showNodePanel.value = false;
    toast.success('已清空编辑器');
  }
};

const activateRouting = () => {
  if (routingStatus.value === 'draft') {
    routingStatus.value = 'active';
    toast.success('工艺路线已激活');
  }
};

const archiveRouting = () => {
  if (routingStatus.value === 'active') {
    routingStatus.value = 'archived';
    toast.success('工艺路线已归档');
  }
};

// Setup connections
onConnect(onConnectHandler);

// Lifecycle
onMounted(() => {
  // Load existing data if editing
  if (props.routingId) {
    // TODO: Load routing data
    console.log('Loading routing:', props.routingId);
  }
});
</script>

<template>
  <div class="h-full flex">
    <!-- Node Palette -->
    <div class="w-64 border-r bg-muted/30 p-4 space-y-4">
      <div>
        <h3 class="font-semibold mb-3">节点工具箱</h3>
        <div class="space-y-2">
          <div
            v-for="nodeType in nodeTypes"
            :key="nodeType.type"
            class="p-3 border rounded-lg cursor-pointer hover:bg-accent transition-colors"
            @click="addNode(nodeType.type)"
          >
            <div class="flex items-center gap-3">
              <div :class="[nodeType.color, 'p-2 rounded text-white']">
                <component :is="nodeType.icon" class="h-4 w-4" />
              </div>
              <div>
                <div class="font-medium text-sm">{{ nodeType.label }}</div>
                <div class="text-xs text-muted-foreground">{{ nodeType.description }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <Separator />

      <!-- Version and Status -->
      <div class="space-y-3">
        <div>
          <label class="text-sm font-medium">版本号</label>
          <input 
            v-model="routingVersion"
            class="w-full mt-1 px-3 py-2 border rounded-md text-sm"
            placeholder="1.0"
          />
        </div>
        
        <div>
          <label class="text-sm font-medium">状态</label>
          <Select v-model="routingStatus">
            <SelectTrigger class="mt-1">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem v-for="option in statusOptions" :key="option.value" :value="option.value">
                {{ option.label }}
              </SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <Separator />

      <!-- Actions -->
      <div class="space-y-2">
        <Button @click="saveRouting" class="w-full gap-2" size="sm">
          <Save class="h-4 w-4" />
          保存路线
        </Button>
        <Button 
          @click="activateRouting" 
          variant="outline" 
          class="w-full gap-2" 
          size="sm"
          :disabled="routingStatus !== 'draft'"
        >
          激活路线
        </Button>
        <Button @click="clearAll" variant="outline" class="w-full gap-2" size="sm">
          <Trash2 class="h-4 w-4" />
          清空画布
        </Button>
      </div>
    </div>

    <!-- Main Editor Area -->
    <div class="flex-1 relative">
      <VueFlow
        v-model:nodes="nodes"
        v-model:edges="edges"
        class="vue-flow-routing-editor"
        :default-viewport="{ zoom: 1 }"
        :min-zoom="0.2"
        :max-zoom="4"
        @node-click="onNodeClick"
        @pane-click="onPaneClick"
      >
        <!-- Top Panel -->
        <Panel :position="PanelPosition.TopRight" class="space-x-2">
          <Badge :variant="statusOptions.find(s => s.value === routingStatus)?.variant">
            {{ statusOptions.find(s => s.value === routingStatus)?.label }}
          </Badge>
          <Button size="sm" variant="outline" :disabled="!canUndo">
            <Undo class="h-4 w-4" />
          </Button>
          <Button size="sm" variant="outline" :disabled="!canRedo">
            <Redo class="h-4 w-4" />
          </Button>
          <Button 
            size="sm" 
            variant="outline" 
            @click="deleteSelectedNode"
            :disabled="!selectedNodeId"
          >
            <Trash2 class="h-4 w-4" />
          </Button>
        </Panel>

        <!-- Bottom Panel -->
        <Panel :position="PanelPosition.BottomLeft">
          <div class="text-sm text-muted-foreground">
            版本: {{ routingVersion }} | 节点: {{ nodes.length }} | 连接: {{ edges.length }}
          </div>
        </Panel>
      </VueFlow>

      <!-- Node Properties Panel -->
      <div 
        v-if="showNodePanel && selectedNode"
        class="absolute top-4 right-4 w-80 bg-background border rounded-lg shadow-lg"
      >
        <Card>
          <CardHeader class="pb-3">
            <CardTitle class="text-lg flex items-center gap-2">
              <Settings class="h-5 w-5" />
              节点属性
            </CardTitle>
          </CardHeader>
          <CardContent class="space-y-4">
            <div>
              <label class="text-sm font-medium">节点标签</label>
              <input 
                v-model="selectedNode.data.label"
                class="w-full mt-1 px-3 py-2 border rounded-md text-sm"
                placeholder="输入节点标签"
              />
            </div>
            
            <div>
              <label class="text-sm font-medium">节点类型</label>
              <Badge variant="outline" class="mt-1">
                {{ getNodeTypeLabel(selectedNode.data.type) }}
              </Badge>
            </div>

            <!-- Dynamic properties based on node type -->
            <div v-if="selectedNode.data.type === 'process-segment'" class="space-y-3">
              <div>
                <label class="text-sm font-medium">关联工艺段</label>
                <input 
                  v-model="selectedNode.data.properties.segmentId"
                  class="w-full mt-1 px-3 py-2 border rounded-md text-sm"
                  placeholder="选择工艺段"
                />
              </div>
              <div>
                <label class="text-sm font-medium">前置时间 (小时)</label>
                <input 
                  v-model.number="selectedNode.data.properties.leadTime"
                  type="number"
                  class="w-full mt-1 px-3 py-2 border rounded-md text-sm"
                />
              </div>
            </div>

            <div v-if="selectedNode.data.type === 'wip-warehouse'" class="space-y-3">
              <div>
                <label class="text-sm font-medium">仓库容量</label>
                <input 
                  v-model.number="selectedNode.data.properties.capacity"
                  type="number"
                  class="w-full mt-1 px-3 py-2 border rounded-md text-sm"
                />
              </div>
              <div>
                <label class="text-sm font-medium">单位</label>
                <select 
                  v-model="selectedNode.data.properties.unit"
                  class="w-full mt-1 px-3 py-2 border rounded-md text-sm"
                >
                  <option value="piece">件</option>
                  <option value="rack">架</option>
                  <option value="m²">平方米</option>
                </select>
              </div>
            </div>

            <div>
              <label class="text-sm font-medium">描述</label>
              <textarea 
                v-model="selectedNode.data.properties.description"
                class="w-full mt-1 px-3 py-2 border rounded-md text-sm"
                rows="3"
                placeholder="输入节点描述"
              />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  </div>
</template>

<style>
@import '@vue-flow/core/dist/style.css';

.vue-flow-routing-editor {
  background-color: #f1f5f9;
}

.vue-flow__node {
  border-radius: 8px;
  border: 2px solid #e2e8f0;
  background: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.vue-flow__node.selected {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.vue-flow__edge {
  stroke: #64748b;
  stroke-width: 2;
}

.vue-flow__edge.selected {
  stroke: #3b82f6;
}
</style>

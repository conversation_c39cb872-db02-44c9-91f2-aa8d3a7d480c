import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import type { 
  Equipment, WorkCenter, Material, ProductFamily, ProcessStep, 
  WipBuffer, ProcessSegment, Routing, WipWarehouse
} from '@/types/masterdata';
import { masterDataService } from '@/services/masterDataService';

export const useMasterDataStore = defineStore('masterData', () => {
  // State
  const loading = ref(false);
  const error = ref<string | null>(null);
  
  const equipments = ref<Equipment[]>([]);
  const workCenters = ref<WorkCenter[]>([]);
  const materials = ref<Material[]>([]);
  const productFamilies = ref<ProductFamily[]>([]);
  const processSteps = ref<ProcessStep[]>([]);
  const wipBuffers = ref<WipBuffer[]>([]);
  const processSegments = ref<ProcessSegment[]>([]);
  const routings = ref<Routing[]>([]);
  const wipWarehouses = ref<WipWarehouse[]>([]);

  // Getters (as functions)
  const getProcessStepById = (id: string) => processSteps.value.find(ps => ps.id === id);
  const getWipBufferById = (id: string) => wipBuffers.value.find(wb => wb.id === id);
  const getProcessSegmentById = (id: string) => processSegments.value.find(ps => ps.id === id);
  const getWipWarehouseById = (id: string) => wipWarehouses.value.find(wh => wh.id === id);

  // Actions
  const setLoading = (value: boolean) => { loading.value = value; };
  const setError = (message: string | null) => { error.value = message; };

  // Generic fetch action
  const fetchData = async <T>(fetcher: () => Promise<T[]>, stateRef: ref<T[]>, errorMessage: string) => {
    if (stateRef.value.length > 0) return; // Avoid refetching
    try {
      setLoading(true);
      setError(null);
      stateRef.value = await fetcher();
    } catch (err) {
      setError(err instanceof Error ? err.message : errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const fetchEquipments = () => fetchData(masterDataService.getEquipments, equipments, '获取设备数据失败');
  const fetchWorkCenters = () => fetchData(masterDataService.getWorkCenters, workCenters, '获取工作中心数据失败');
  const fetchMaterials = () => fetchData(masterDataService.getMaterials, materials, '获取物料数据失败');
  const fetchProductFamilies = () => fetchData(masterDataService.getProductFamilies, productFamilies, '获取产品族数据失败');
  const fetchProcessSteps = () => fetchData(masterDataService.getProcessSteps, processSteps, '获取工序数据失败');
  const fetchWipBuffers = () => fetchData(masterDataService.getWipBuffers, wipBuffers, '获取WIP缓冲区数据失败');
  const fetchProcessSegments = () => fetchData(masterDataService.getProcessSegments, processSegments, '获取工艺段数据失败');
  const fetchRoutings = () => fetchData(masterDataService.getRoutings, routings, '获取工艺路线数据失败');
  const fetchWipWarehouses = () => fetchData(masterDataService.getWipWarehouses, wipWarehouses, '获取WIP仓库数据失败');

  // Generic CRUD actions
  const createEntity = async <T extends { id: string }>(creator: (data: any) => Promise<T>, data: Omit<T, 'id'>, stateRef: ref<T[]>, errorMessage: string) => {
    try {
      setLoading(true);
      const newItem = await creator(data);
      stateRef.value.push(newItem);
      return newItem;
    } catch (err) {
      setError(err instanceof Error ? err.message : errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const updateEntity = async <T extends { id: string }>(updater: (id: string, data: any) => Promise<T>, id: string, data: Partial<T>, stateRef: ref<T[]>, errorMessage: string) => {
    try {
      setLoading(true);
      const updatedItem = await updater(id, data);
      const index = stateRef.value.findIndex(item => item.id === id);
      if (index !== -1) {
        stateRef.value[index] = updatedItem;
      }
      return updatedItem;
    } catch (err) {
      setError(err instanceof Error ? err.message : errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const deleteEntity = async (deleter: (id: string) => Promise<boolean>, id: string, stateRef: ref<any[]>, errorMessage: string) => {
    try {
      setLoading(true);
      await deleter(id);
      stateRef.value = stateRef.value.filter(item => item.id !== id);
      return true;
    } catch (err) {
      setError(err instanceof Error ? err.message : errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Routing actions
  const createRouting = (data: Omit<Routing, 'id'>) => createEntity(masterDataService.createRouting, data, routings, '创建工艺路线失败');
  const updateRouting = (id: string, data: Partial<Routing>) => updateEntity(masterDataService.updateRouting, id, data, routings, '更新工艺路线失败');
  const deleteRouting = (id: string) => deleteEntity(masterDataService.deleteRouting, id, routings, '删除工艺路线失败');

  // ProcessSegment actions
  const createProcessSegment = (data: Omit<ProcessSegment, 'id'>) => createEntity(masterDataService.createProcessSegment, data, processSegments, '创建工艺段失败');
  const updateProcessSegment = (id: string, data: Partial<ProcessSegment>) => updateEntity(masterDataService.updateProcessSegment, id, data, processSegments, '更新工艺段失败');
  const deleteProcessSegment = (id: string) => deleteEntity(masterDataService.deleteProcessSegment, id, processSegments, '删除工艺段失败');

  return {
    // State
    loading,
    error,
    equipments,
    workCenters,
    materials,
    productFamilies,
    processSteps,
    wipBuffers,
    processSegments,
    routings,
    wipWarehouses,
    
    // Getters
    getProcessStepById,
    getWipBufferById,
    getProcessSegmentById,
    getWipWarehouseById,
    
    // Actions
    fetchEquipments,
    fetchWorkCenters,
    fetchMaterials,
    fetchProductFamilies,
    fetchProcessSteps,
    fetchWipBuffers,
    fetchProcessSegments,
    fetchRoutings,
    fetchWipWarehouses,

    createRouting,
    updateRouting,
    deleteRouting,
    createProcessSegment,
    updateProcessSegment,
    deleteProcessSegment,
  };
});

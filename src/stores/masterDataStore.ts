import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import type { 
  Equipment, WorkCenter, Material, ProductFamily, ProcessStep, 
  WipBuffer, ProcessSegment, Routing, ValidationResult 
} from '@/types/masterdata';
import { masterDataService } from '@/services/masterDataService';

export const useMasterDataStore = defineStore('masterData', () => {
  // State
  const loading = ref(false);
  const error = ref<string | null>(null);
  
  // Equipment state
  const equipments = ref<Equipment[]>([]);
  const selectedEquipment = ref<Equipment | null>(null);
  
  // WorkCenter state
  const workCenters = ref<WorkCenter[]>([]);
  const selectedWorkCenter = ref<WorkCenter | null>(null);
  
  // Material state
  const materials = ref<Material[]>([]);
  const selectedMaterial = ref<Material | null>(null);
  
  // ProductFamily state
  const productFamilies = ref<ProductFamily[]>([]);
  const selectedProductFamily = ref<ProductFamily | null>(null);
  
  // ProcessStep state
  const processSteps = ref<ProcessStep[]>([]);
  const selectedProcessStep = ref<ProcessStep | null>(null);
  
  // WipBuffer state
  const wipBuffers = ref<WipBuffer[]>([]);
  const selectedWipBuffer = ref<WipBuffer | null>(null);
  
  // ProcessSegment state
  const processSegments = ref<ProcessSegment[]>([]);
  const selectedProcessSegment = ref<ProcessSegment | null>(null);
  
  // Routing state
  const routings = ref<Routing[]>([]);
  const selectedRouting = ref<Routing | null>(null);

  // Computed
  const activeEquipments = computed(() => 
    equipments.value.filter(eq => eq.status !== 'maintenance')
  );
  
  const activeMaterials = computed(() => 
    materials.value.filter(m => m.isActive !== false)
  );
  
  const activeProcessSteps = computed(() => 
    processSteps.value.filter(ps => ps.isActive !== false)
  );

  // Actions
  const setLoading = (value: boolean) => {
    loading.value = value;
  };

  const setError = (message: string | null) => {
    error.value = message;
  };

  // Equipment actions
  const fetchEquipments = async () => {
    try {
      setLoading(true);
      setError(null);
      equipments.value = await masterDataService.getEquipments();
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取设备数据失败');
    } finally {
      setLoading(false);
    }
  };

  const createEquipment = async (equipment: Omit<Equipment, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      setLoading(true);
      setError(null);
      const newEquipment = await masterDataService.createEquipment(equipment);
      equipments.value.push(newEquipment);
      return newEquipment;
    } catch (err) {
      setError(err instanceof Error ? err.message : '创建设备失败');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const updateEquipment = async (id: string, updates: Partial<Equipment>) => {
    try {
      setLoading(true);
      setError(null);
      const updatedEquipment = await masterDataService.updateEquipment(id, updates);
      if (updatedEquipment) {
        const index = equipments.value.findIndex(eq => eq.id === id);
        if (index !== -1) {
          equipments.value[index] = updatedEquipment;
        }
        if (selectedEquipment.value?.id === id) {
          selectedEquipment.value = updatedEquipment;
        }
      }
      return updatedEquipment;
    } catch (err) {
      setError(err instanceof Error ? err.message : '更新设备失败');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const deleteEquipment = async (id: string) => {
    try {
      setLoading(true);
      setError(null);
      const success = await masterDataService.deleteEquipment(id);
      if (success) {
        equipments.value = equipments.value.filter(eq => eq.id !== id);
        if (selectedEquipment.value?.id === id) {
          selectedEquipment.value = null;
        }
      }
      return success;
    } catch (err) {
      setError(err instanceof Error ? err.message : '删除设备失败');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // WorkCenter actions
  const fetchWorkCenters = async () => {
    try {
      setLoading(true);
      setError(null);
      workCenters.value = await masterDataService.getWorkCenters();
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取工作中心数据失败');
    } finally {
      setLoading(false);
    }
  };

  const createWorkCenter = async (workCenter: Omit<WorkCenter, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      setLoading(true);
      setError(null);
      const newWorkCenter = await masterDataService.createWorkCenter(workCenter);
      workCenters.value.push(newWorkCenter);
      return newWorkCenter;
    } catch (err) {
      setError(err instanceof Error ? err.message : '创建工作中心失败');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const updateWorkCenter = async (id: string, updates: Partial<WorkCenter>) => {
    try {
      setLoading(true);
      setError(null);
      const updatedWorkCenter = await masterDataService.updateWorkCenter(id, updates);
      if (updatedWorkCenter) {
        const index = workCenters.value.findIndex(wc => wc.id === id);
        if (index !== -1) {
          workCenters.value[index] = updatedWorkCenter;
        }
        if (selectedWorkCenter.value?.id === id) {
          selectedWorkCenter.value = updatedWorkCenter;
        }
      }
      return updatedWorkCenter;
    } catch (err) {
      setError(err instanceof Error ? err.message : '更新工作中心失败');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const deleteWorkCenter = async (id: string) => {
    try {
      setLoading(true);
      setError(null);
      const success = await masterDataService.deleteWorkCenter(id);
      if (success) {
        workCenters.value = workCenters.value.filter(wc => wc.id !== id);
        if (selectedWorkCenter.value?.id === id) {
          selectedWorkCenter.value = null;
        }
      }
      return success;
    } catch (err) {
      setError(err instanceof Error ? err.message : '删除工作中心失败');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Material actions
  const fetchMaterials = async () => {
    try {
      setLoading(true);
      setError(null);
      materials.value = await masterDataService.getMaterials();
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取物料数据失败');
    } finally {
      setLoading(false);
    }
  };

  const createMaterial = async (material: Omit<Material, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      setLoading(true);
      setError(null);
      const newMaterial = await masterDataService.createMaterial(material);
      materials.value.push(newMaterial);
      return newMaterial;
    } catch (err) {
      setError(err instanceof Error ? err.message : '创建物料失败');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const updateMaterial = async (id: string, updates: Partial<Material>) => {
    try {
      setLoading(true);
      setError(null);
      const updatedMaterial = await masterDataService.updateMaterial(id, updates);
      if (updatedMaterial) {
        const index = materials.value.findIndex(m => m.id === id);
        if (index !== -1) {
          materials.value[index] = updatedMaterial;
        }
        if (selectedMaterial.value?.id === id) {
          selectedMaterial.value = updatedMaterial;
        }
      }
      return updatedMaterial;
    } catch (err) {
      setError(err instanceof Error ? err.message : '更新物料失败');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const deleteMaterial = async (id: string) => {
    try {
      setLoading(true);
      setError(null);
      const success = await masterDataService.deleteMaterial(id);
      if (success) {
        materials.value = materials.value.filter(m => m.id !== id);
        if (selectedMaterial.value?.id === id) {
          selectedMaterial.value = null;
        }
      }
      return success;
    } catch (err) {
      setError(err instanceof Error ? err.message : '删除物料失败');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // ProcessStep actions
  const fetchProcessSteps = async () => {
    try {
      setLoading(true);
      setError(null);
      processSteps.value = await masterDataService.getProcessSteps();
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取工序数据失败');
    } finally {
      setLoading(false);
    }
  };

  const createProcessStep = async (processStep: Omit<ProcessStep, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      setLoading(true);
      setError(null);
      const newProcessStep = await masterDataService.createProcessStep(processStep);
      processSteps.value.push(newProcessStep);
      return newProcessStep;
    } catch (err) {
      setError(err instanceof Error ? err.message : '创建工序失败');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const updateProcessStep = async (id: string, updates: Partial<ProcessStep>) => {
    try {
      setLoading(true);
      setError(null);
      const updatedProcessStep = await masterDataService.updateProcessStep(id, updates);
      if (updatedProcessStep) {
        const index = processSteps.value.findIndex(ps => ps.id === id);
        if (index !== -1) {
          processSteps.value[index] = updatedProcessStep;
        }
        if (selectedProcessStep.value?.id === id) {
          selectedProcessStep.value = updatedProcessStep;
        }
      }
      return updatedProcessStep;
    } catch (err) {
      setError(err instanceof Error ? err.message : '更新工序失败');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const deleteProcessStep = async (id: string) => {
    try {
      setLoading(true);
      setError(null);
      const success = await masterDataService.deleteProcessStep(id);
      if (success) {
        processSteps.value = processSteps.value.filter(ps => ps.id !== id);
        if (selectedProcessStep.value?.id === id) {
          selectedProcessStep.value = null;
        }
      }
      return success;
    } catch (err) {
      setError(err instanceof Error ? err.message : '删除工序失败');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // ProductFamily actions
  const fetchProductFamilies = async () => {
    try {
      setLoading(true);
      setError(null);
      productFamilies.value = await masterDataService.getProductFamilies();
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取产品族数据失败');
    } finally {
      setLoading(false);
    }
  };

  const createProductFamily = async (productFamily: Omit<ProductFamily, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      setLoading(true);
      setError(null);
      const newProductFamily = await masterDataService.createProductFamily(productFamily);
      productFamilies.value.push(newProductFamily);
      return newProductFamily;
    } catch (err) {
      setError(err instanceof Error ? err.message : '创建产品族失败');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const updateProductFamily = async (id: string, updates: Partial<ProductFamily>) => {
    try {
      setLoading(true);
      setError(null);
      const updatedProductFamily = await masterDataService.updateProductFamily(id, updates);
      if (updatedProductFamily) {
        const index = productFamilies.value.findIndex(pf => pf.id === id);
        if (index !== -1) {
          productFamilies.value[index] = updatedProductFamily;
        }
        if (selectedProductFamily.value?.id === id) {
          selectedProductFamily.value = updatedProductFamily;
        }
      }
      return updatedProductFamily;
    } catch (err) {
      setError(err instanceof Error ? err.message : '更新产品族失败');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const deleteProductFamily = async (id: string) => {
    try {
      setLoading(true);
      setError(null);
      const success = await masterDataService.deleteProductFamily(id);
      if (success) {
        productFamilies.value = productFamilies.value.filter(pf => pf.id !== id);
        if (selectedProductFamily.value?.id === id) {
          selectedProductFamily.value = null;
        }
      }
      return success;
    } catch (err) {
      setError(err instanceof Error ? err.message : '删除产品族失败');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // ProcessSegment actions
  const fetchProcessSegments = async () => {
    try {
      setLoading(true);
      setError(null);
      processSegments.value = await masterDataService.getProcessSegments();
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取工艺段数据失败');
    } finally {
      setLoading(false);
    }
  };

  const createProcessSegment = async (processSegment: Omit<ProcessSegment, 'id'>) => {
    try {
      setLoading(true);
      setError(null);
      const newProcessSegment = await masterDataService.createProcessSegment(processSegment);
      processSegments.value.push(newProcessSegment);
      return newProcessSegment;
    } catch (err) {
      setError(err instanceof Error ? err.message : '创建工艺段失败');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const updateProcessSegment = async (id: string, updates: Partial<ProcessSegment>) => {
    try {
      setLoading(true);
      setError(null);
      const updatedProcessSegment = await masterDataService.updateProcessSegment(id, updates);
      if (updatedProcessSegment) {
        const index = processSegments.value.findIndex(ps => ps.id === id);
        if (index !== -1) {
          processSegments.value[index] = updatedProcessSegment;
        }
        if (selectedProcessSegment.value?.id === id) {
          selectedProcessSegment.value = updatedProcessSegment;
        }
      }
      return updatedProcessSegment;
    } catch (err) {
      setError(err instanceof Error ? err.message : '更新工艺段失败');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const deleteProcessSegment = async (id: string) => {
    try {
      setLoading(true);
      setError(null);
      const success = await masterDataService.deleteProcessSegment(id);
      if (success) {
        processSegments.value = processSegments.value.filter(ps => ps.id !== id);
        if (selectedProcessSegment.value?.id === id) {
          selectedProcessSegment.value = null;
        }
      }
      return success;
    } catch (err) {
      setError(err instanceof Error ? err.message : '删除工艺段失败');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Routing actions
  const fetchRoutings = async () => {
    try {
      setLoading(true);
      setError(null);
      routings.value = await masterDataService.getRoutings();
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取工艺路线数据失败');
    } finally {
      setLoading(false);
    }
  };

  const createRouting = async (routing: Omit<Routing, 'id'>) => {
    try {
      setLoading(true);
      setError(null);
      const newRouting = await masterDataService.createRouting(routing);
      routings.value.push(newRouting);
      return newRouting;
    } catch (err) {
      setError(err instanceof Error ? err.message : '创建工艺路线失败');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const updateRouting = async (id: string, updates: Partial<Routing>) => {
    try {
      setLoading(true);
      setError(null);
      const updatedRouting = await masterDataService.updateRouting(id, updates);
      if (updatedRouting) {
        const index = routings.value.findIndex(r => r.id === id);
        if (index !== -1) {
          routings.value[index] = updatedRouting;
        }
        if (selectedRouting.value?.id === id) {
          selectedRouting.value = updatedRouting;
        }
      }
      return updatedRouting;
    } catch (err) {
      setError(err instanceof Error ? err.message : '更新工艺路线失败');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const deleteRouting = async (id: string) => {
    try {
      setLoading(true);
      setError(null);
      const success = await masterDataService.deleteRouting(id);
      if (success) {
        routings.value = routings.value.filter(r => r.id !== id);
        if (selectedRouting.value?.id === id) {
          selectedRouting.value = null;
        }
      }
      return success;
    } catch (err) {
      setError(err instanceof Error ? err.message : '删除工艺路线失败');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Search actions
  const searchEquipments = async (query: string) => {
    try {
      setLoading(true);
      setError(null);
      return await masterDataService.searchEquipments(query);
    } catch (err) {
      setError(err instanceof Error ? err.message : '搜索设备失败');
      return [];
    } finally {
      setLoading(false);
    }
  };

  const searchMaterials = async (query: string) => {
    try {
      setLoading(true);
      setError(null);
      return await masterDataService.searchMaterials(query);
    } catch (err) {
      setError(err instanceof Error ? err.message : '搜索物料失败');
      return [];
    } finally {
      setLoading(false);
    }
  };

  // Selection actions
  const selectEquipment = (equipment: Equipment | null) => {
    selectedEquipment.value = equipment;
  };

  const selectMaterial = (material: Material | null) => {
    selectedMaterial.value = material;
  };

  const selectProcessStep = (processStep: ProcessStep | null) => {
    selectedProcessStep.value = processStep;
  };

  const clearSelections = () => {
    selectedEquipment.value = null;
    selectedMaterial.value = null;
    selectedProcessStep.value = null;
    selectedWorkCenter.value = null;
    selectedProductFamily.value = null;
    selectedWipBuffer.value = null;
    selectedProcessSegment.value = null;
    selectedRouting.value = null;
  };

  return {
    // State
    loading,
    error,
    equipments,
    selectedEquipment,
    workCenters,
    selectedWorkCenter,
    materials,
    selectedMaterial,
    productFamilies,
    selectedProductFamily,
    processSteps,
    selectedProcessStep,
    wipBuffers,
    selectedWipBuffer,
    processSegments,
    selectedProcessSegment,
    routings,
    selectedRouting,
    
    // Computed
    activeEquipments,
    activeMaterials,
    activeProcessSteps,
    
    // Actions
    setLoading,
    setError,
    fetchEquipments,
    createEquipment,
    updateEquipment,
    deleteEquipment,
    fetchWorkCenters,
    createWorkCenter,
    updateWorkCenter,
    deleteWorkCenter,
    fetchMaterials,
    createMaterial,
    updateMaterial,
    deleteMaterial,
    fetchProcessSteps,
    createProcessStep,
    updateProcessStep,
    deleteProcessStep,
    fetchProductFamilies,
    createProductFamily,
    updateProductFamily,
    deleteProductFamily,
    fetchProcessSegments,
    createProcessSegment,
    updateProcessSegment,
    deleteProcessSegment,
    fetchRoutings,
    createRouting,
    updateRouting,
    deleteRouting,
    searchEquipments,
    searchMaterials,
    selectEquipment,
    selectMaterial,
    selectProcessStep,
    clearSelections
  };
});

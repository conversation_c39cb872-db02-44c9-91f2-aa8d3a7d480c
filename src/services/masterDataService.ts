// src/services/masterDataService.ts
import type {
  Equipment, WorkCenter, Material, ProductFamily, ProcessStep, WipBuffer,
  ProcessSegment, WipWarehouse, Routing
} from '@/types/masterdata';

// 使用 Vite 的 import.meta.glob 直接加载所有 mock 数据
// 这样更可靠，避免了在不同部署环境下 fetch 路径的问题
const modules = import.meta.glob('/public/mock/masterdata/*.json');

class MasterDataService {
  // 内存数据存储，模拟数据库
  private dataStore = new Map<string, any[]>();
  private initialized = false;

  // 一次性初始化所有数据
  private async initializeDataStore(): Promise<void> {
    if (this.initialized) return;

    for (const path in modules) {
      const key = path.replace('/public/mock/masterdata/', '').replace('.json', '');
      const module: any = await modules[path]();
      this.dataStore.set(key, module.default || []);
    }
    this.initialized = true;
  }

  private async getStore<T>(key: string): Promise<T[]> {
    await this.initializeDataStore();
    return this.dataStore.get(key) || [];
  }

  private generateId(prefix: string): string {
    return `${prefix}-${Date.now()}`;
  }

  // --- 通用 CRUD ---
  private async createEntity<T extends { id: string }>(storeKey: string, entityData: Omit<T, 'id'>, idPrefix: string): Promise<T> {
    const store = await this.getStore<T>(storeKey);
    const newItem = {
      ...entityData,
      id: this.generateId(idPrefix),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    } as T;
    store.push(newItem);
    this.dataStore.set(storeKey, store);
    return newItem;
  }

  private async updateEntity<T extends { id: string }>(storeKey: string, id: string, updates: Partial<T>): Promise<T | null> {
    const store = await this.getStore<T>(storeKey);
    const index = store.findIndex(item => item.id === id);
    if (index === -1) return null;
    store[index] = { ...store[index], ...updates, updatedAt: new Date().toISOString() };
    this.dataStore.set(storeKey, store);
    return store[index];
  }

  private async deleteEntity(storeKey: string, id: string): Promise<boolean> {
    const store = await this.getStore<any>(storeKey);
    const initialLength = store.length;
    const newStore = store.filter(item => item.id !== id);
    if (newStore.length === initialLength) return false;
    this.dataStore.set(storeKey, newStore);
    return true;
  }

  // --- API 方法 ---
  
  getEquipments = () => this.getStore<Equipment>('equipments');
  createEquipment = (data: Omit<Equipment, 'id'>) => this.createEntity('equipments', data, 'EQ');
  updateEquipment = (id: string, data: Partial<Equipment>) => this.updateEntity('equipments', id, data);
  deleteEquipment = (id: string) => this.deleteEntity('equipments', id);

  getWorkCenters = () => this.getStore<WorkCenter>('workCenters');
  createWorkCenter = (data: Omit<WorkCenter, 'id'>) => this.createEntity('workCenters', data, 'WC');
  updateWorkCenter = (id: string, data: Partial<WorkCenter>) => this.updateEntity('workCenters', id, data);
  deleteWorkCenter = (id: string) => this.deleteEntity('workCenters', id);

  getMaterials = () => this.getStore<Material>('materials');
  createMaterial = (data: Omit<Material, 'id'>) => this.createEntity('materials', data, 'MAT');
  updateMaterial = (id: string, data: Partial<Material>) => this.updateEntity('materials', id, data);
  deleteMaterial = (id: string) => this.deleteEntity('materials', id);

  getProductFamilies = () => this.getStore<ProductFamily>('productFamilies');
  createProductFamily = (data: Omit<ProductFamily, 'id'>) => this.createEntity('productFamilies', data, 'PF');
  updateProductFamily = (id: string, data: Partial<ProductFamily>) => this.updateEntity('productFamilies', id, data);
  deleteProductFamily = (id: string) => this.deleteEntity('productFamilies', id);

  getProcessSteps = () => this.getStore<ProcessStep>('processSteps');
  createProcessStep = (data: Omit<ProcessStep, 'id'>) => this.createEntity('processSteps', data, 'PS');
  updateProcessStep = (id: string, data: Partial<ProcessStep>) => this.updateEntity('processSteps', id, data);
  deleteProcessStep = (id: string) => this.deleteEntity('processSteps', id);

  getWipBuffers = () => this.getStore<WipBuffer>('wipBuffers');
  createWipBuffer = (data: Omit<WipBuffer, 'id'>) => this.createEntity('wipBuffers', data, 'BUF');
  updateWipBuffer = (id: string, data: Partial<WipBuffer>) => this.updateEntity('wipBuffers', id, data);
  deleteWipBuffer = (id: string) => this.deleteEntity('wipBuffers', id);

  getProcessSegments = () => this.getStore<ProcessSegment>('processSegments');
  createProcessSegment = (data: Omit<ProcessSegment, 'id'>) => this.createEntity('processSegments', data, 'SEG');
  updateProcessSegment = (id: string, data: Partial<ProcessSegment>) => this.updateEntity('processSegments', id, data);
  deleteProcessSegment = (id: string) => this.deleteEntity('processSegments', id);

  getRoutings = () => this.getStore<Routing>('routings');
  createRouting = (data: Omit<Routing, 'id'>) => this.createEntity('routings', data, 'RT');
  updateRouting = (id: string, data: Partial<Routing>) => this.updateEntity('routings', id, data);
  deleteRouting = (id: string) => this.deleteEntity('routings', id);

  getWipWarehouses = () => this.getStore<WipWarehouse>('wipWarehouses');
}

export const masterDataService = new MasterDataService();

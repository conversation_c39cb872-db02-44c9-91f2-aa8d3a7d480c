// src/services/masterDataService.ts

import type {
  Equipment, WorkCenter, Material, ProductFamily, ProcessStep, WipBuffer,
  ProcessSegment, WipWarehouse, Routing, Calendar, ParametricBOM, ProjectInstance,
  ValidationResult, BatchOperation, BatchOperationResult
} from '@/types/masterdata';

class MasterDataService {
  // 模拟数据存储（原型阶段使用内存存储）
  private dataStore = new Map<string, any[]>();

  private async fetchData<T>(url: string): Promise<T> {
    try {
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`Network response was not ok for ${url}`);
      }
      const data = await response.json() as T;

      // 将数据存储到内存中以支持CRUD操作
      const key = this.getStoreKey(url);
      if (Array.isArray(data)) {
        this.dataStore.set(key, [...data]);
      }

      return data;
    } catch (error) {
      console.error(`Failed to fetch data from ${url}:`, error);
      // 在原型阶段，如果fetch失败，可以返回一个空数组或默认对象，以避免UI崩溃
      return [] as T;
    }
  }

  private getStoreKey(url: string): string {
    return url.replace('/mock/masterdata/', '').replace('.json', '');
  }

  private generateId(prefix: string): string {
    return `${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private async saveToStore<T>(key: string, data: T[]): Promise<void> {
    this.dataStore.set(key, [...data]);
    // 在真实环境中，这里会调用后端API保存数据
    console.log(`Saved ${data.length} items to ${key}`);
  }

  // --- 模块一: 制造资源管理 ---

  // 设备管理
  async getEquipments(): Promise<Equipment[]> {
    return this.fetchData<Equipment[]>('/mock/masterdata/equipments.json');
  }

  async getEquipmentById(id: string): Promise<Equipment | null> {
    const equipments = await this.getEquipments();
    return equipments.find(eq => eq.id === id) || null;
  }

  async createEquipment(equipment: Omit<Equipment, 'id' | 'createdAt' | 'updatedAt'>): Promise<Equipment> {
    const equipments = await this.getEquipments();
    const newEquipment: Equipment = {
      ...equipment,
      id: this.generateId('EQ'),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    equipments.push(newEquipment);
    await this.saveToStore('equipments', equipments);
    return newEquipment;
  }

  async updateEquipment(id: string, updates: Partial<Equipment>): Promise<Equipment | null> {
    const equipments = await this.getEquipments();
    const index = equipments.findIndex(eq => eq.id === id);

    if (index === -1) return null;

    equipments[index] = {
      ...equipments[index],
      ...updates,
      updatedAt: new Date().toISOString()
    };

    await this.saveToStore('equipments', equipments);
    return equipments[index];
  }

  async deleteEquipment(id: string): Promise<boolean> {
    const equipments = await this.getEquipments();
    const index = equipments.findIndex(eq => eq.id === id);

    if (index === -1) return false;

    equipments.splice(index, 1);
    await this.saveToStore('equipments', equipments);
    return true;
  }

  // 工作中心管理
  async getWorkCenters(): Promise<WorkCenter[]> {
    return this.fetchData<WorkCenter[]>('/mock/masterdata/workCenters.json');
  }

  async getWorkCenterById(id: string): Promise<WorkCenter | null> {
    const workCenters = await this.getWorkCenters();
    return workCenters.find(wc => wc.id === id) || null;
  }

  async createWorkCenter(workCenter: Omit<WorkCenter, 'id' | 'createdAt' | 'updatedAt'>): Promise<WorkCenter> {
    const workCenters = await this.getWorkCenters();
    const newWorkCenter: WorkCenter = {
      ...workCenter,
      id: this.generateId('WC'),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    workCenters.push(newWorkCenter);
    await this.saveToStore('workCenters', workCenters);
    return newWorkCenter;
  }

  async updateWorkCenter(id: string, updates: Partial<WorkCenter>): Promise<WorkCenter | null> {
    const workCenters = await this.getWorkCenters();
    const index = workCenters.findIndex(wc => wc.id === id);

    if (index === -1) return null;

    workCenters[index] = {
      ...workCenters[index],
      ...updates,
      updatedAt: new Date().toISOString()
    };

    await this.saveToStore('workCenters', workCenters);
    return workCenters[index];
  }

  async deleteWorkCenter(id: string): Promise<boolean> {
    const workCenters = await this.getWorkCenters();
    const index = workCenters.findIndex(wc => wc.id === id);

    if (index === -1) return false;

    workCenters.splice(index, 1);
    await this.saveToStore('workCenters', workCenters);
    return true;
  }

  // --- 模块二: 产品与物料工程 ---

  // 物料管理
  async getMaterials(): Promise<Material[]> {
    return this.fetchData<Material[]>('/mock/masterdata/materials.json');
  }

  async getMaterialById(id: string): Promise<Material | null> {
    const materials = await this.getMaterials();
    return materials.find(m => m.id === id) || null;
  }

  async createMaterial(material: Omit<Material, 'id' | 'createdAt' | 'updatedAt'>): Promise<Material> {
    const materials = await this.getMaterials();
    const newMaterial: Material = {
      ...material,
      id: this.generateId('MAT'),
      isActive: material.isActive ?? true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    materials.push(newMaterial);
    await this.saveToStore('materials', materials);
    return newMaterial;
  }

  async updateMaterial(id: string, updates: Partial<Material>): Promise<Material | null> {
    const materials = await this.getMaterials();
    const index = materials.findIndex(m => m.id === id);

    if (index === -1) return null;

    materials[index] = {
      ...materials[index],
      ...updates,
      updatedAt: new Date().toISOString()
    };

    await this.saveToStore('materials', materials);
    return materials[index];
  }

  async deleteMaterial(id: string): Promise<boolean> {
    const materials = await this.getMaterials();
    const index = materials.findIndex(m => m.id === id);

    if (index === -1) return false;

    materials.splice(index, 1);
    await this.saveToStore('materials', materials);
    return true;
  }

  // 产品族管理
  async getProductFamilies(): Promise<ProductFamily[]> {
    return this.fetchData<ProductFamily[]>('/mock/masterdata/productFamilies.json');
  }

  async getProductFamilyById(id: string): Promise<ProductFamily | null> {
    const productFamilies = await this.getProductFamilies();
    return productFamilies.find(pf => pf.id === id) || null;
  }

  async createProductFamily(productFamily: Omit<ProductFamily, 'id' | 'createdAt' | 'updatedAt'>): Promise<ProductFamily> {
    const productFamilies = await this.getProductFamilies();
    const newProductFamily: ProductFamily = {
      ...productFamily,
      id: this.generateId('PF'),
      isActive: productFamily.isActive ?? true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    productFamilies.push(newProductFamily);
    await this.saveToStore('productFamilies', productFamilies);
    return newProductFamily;
  }

  async updateProductFamily(id: string, updates: Partial<ProductFamily>): Promise<ProductFamily | null> {
    const productFamilies = await this.getProductFamilies();
    const index = productFamilies.findIndex(pf => pf.id === id);

    if (index === -1) return null;

    productFamilies[index] = {
      ...productFamilies[index],
      ...updates,
      updatedAt: new Date().toISOString()
    };

    await this.saveToStore('productFamilies', productFamilies);
    return productFamilies[index];
  }

  async deleteProductFamily(id: string): Promise<boolean> {
    const productFamilies = await this.getProductFamilies();
    const index = productFamilies.findIndex(pf => pf.id === id);

    if (index === -1) return false;

    productFamilies.splice(index, 1);
    await this.saveToStore('productFamilies', productFamilies);
    return true;
  }

  // --- 模块三: 分段式工艺工程 ---

  // 工序管理
  async getProcessSteps(): Promise<ProcessStep[]> {
    return this.fetchData<ProcessStep[]>('/mock/masterdata/processSteps.json');
  }

  async getProcessStepById(id: string): Promise<ProcessStep | null> {
    const processSteps = await this.getProcessSteps();
    return processSteps.find(ps => ps.id === id) || null;
  }

  async createProcessStep(processStep: Omit<ProcessStep, 'id' | 'createdAt' | 'updatedAt'>): Promise<ProcessStep> {
    const processSteps = await this.getProcessSteps();
    const newProcessStep: ProcessStep = {
      ...processStep,
      id: this.generateId('PS'),
      isActive: processStep.isActive ?? true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    processSteps.push(newProcessStep);
    await this.saveToStore('processSteps', processSteps);
    return newProcessStep;
  }

  async updateProcessStep(id: string, updates: Partial<ProcessStep>): Promise<ProcessStep | null> {
    const processSteps = await this.getProcessSteps();
    const index = processSteps.findIndex(ps => ps.id === id);

    if (index === -1) return null;

    processSteps[index] = {
      ...processSteps[index],
      ...updates,
      updatedAt: new Date().toISOString()
    };

    await this.saveToStore('processSteps', processSteps);
    return processSteps[index];
  }

  async deleteProcessStep(id: string): Promise<boolean> {
    const processSteps = await this.getProcessSteps();
    const index = processSteps.findIndex(ps => ps.id === id);

    if (index === -1) return false;

    processSteps.splice(index, 1);
    await this.saveToStore('processSteps', processSteps);
    return true;
  }

  // WIP缓冲区管理
  async getWipBuffers(): Promise<WipBuffer[]> {
    return this.fetchData<WipBuffer[]>('/mock/masterdata/wipBuffers.json');
  }

  async getWipBufferById(id: string): Promise<WipBuffer | null> {
    const wipBuffers = await this.getWipBuffers();
    return wipBuffers.find(wb => wb.id === id) || null;
  }

  async createWipBuffer(wipBuffer: Omit<WipBuffer, 'id' | 'createdAt' | 'updatedAt'>): Promise<WipBuffer> {
    const wipBuffers = await this.getWipBuffers();
    const newWipBuffer: WipBuffer = {
      ...wipBuffer,
      id: this.generateId('BUF'),
      isActive: wipBuffer.isActive ?? true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    wipBuffers.push(newWipBuffer);
    await this.saveToStore('wipBuffers', wipBuffers);
    return newWipBuffer;
  }

  async updateWipBuffer(id: string, updates: Partial<WipBuffer>): Promise<WipBuffer | null> {
    const wipBuffers = await this.getWipBuffers();
    const index = wipBuffers.findIndex(wb => wb.id === id);

    if (index === -1) return null;

    wipBuffers[index] = {
      ...wipBuffers[index],
      ...updates,
      updatedAt: new Date().toISOString()
    };

    await this.saveToStore('wipBuffers', wipBuffers);
    return wipBuffers[index];
  }

  async deleteWipBuffer(id: string): Promise<boolean> {
    const wipBuffers = await this.getWipBuffers();
    const index = wipBuffers.findIndex(wb => wb.id === id);

    if (index === -1) return false;

    wipBuffers.splice(index, 1);
    await this.saveToStore('wipBuffers', wipBuffers);
    return true;
  }

  // 工艺段管理
  async getProcessSegments(): Promise<ProcessSegment[]> {
    return this.fetchData<ProcessSegment[]>('/mock/masterdata/processSegments.json');
  }

  async getProcessSegmentById(id: string): Promise<ProcessSegment | null> {
    const processSegments = await this.getProcessSegments();
    return processSegments.find(ps => ps.id === id) || null;
  }

  async createProcessSegment(processSegment: Omit<ProcessSegment, 'id'>): Promise<ProcessSegment> {
    const processSegments = await this.getProcessSegments();
    const newProcessSegment: ProcessSegment = {
      ...processSegment,
      id: this.generateId('SEG')
    };

    processSegments.push(newProcessSegment);
    await this.saveToStore('processSegments', processSegments);
    return newProcessSegment;
  }

  async updateProcessSegment(id: string, updates: Partial<ProcessSegment>): Promise<ProcessSegment | null> {
    const processSegments = await this.getProcessSegments();
    const index = processSegments.findIndex(ps => ps.id === id);

    if (index === -1) return null;

    processSegments[index] = {
      ...processSegments[index],
      ...updates
    };

    await this.saveToStore('processSegments', processSegments);
    return processSegments[index];
  }

  async deleteProcessSegment(id: string): Promise<boolean> {
    const processSegments = await this.getProcessSegments();
    const index = processSegments.findIndex(ps => ps.id === id);

    if (index === -1) return false;

    processSegments.splice(index, 1);
    await this.saveToStore('processSegments', processSegments);
    return true;
  }

  // 工艺路线管理
  async getRoutings(): Promise<Routing[]> {
    return this.fetchData<Routing[]>('/mock/masterdata/routings.json');
  }

  async getRoutingById(id: string): Promise<Routing | null> {
    const routings = await this.getRoutings();
    return routings.find(r => r.id === id) || null;
  }

  async createRouting(routing: Omit<Routing, 'id' | 'createdAt' | 'updatedAt'>): Promise<Routing> {
    const routings = await this.getRoutings();
    const newRouting: Routing = {
      ...routing,
      id: this.generateId('RT'),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    routings.push(newRouting);
    await this.saveToStore('routings', routings);
    return newRouting;
  }

  async updateRouting(id: string, updates: Partial<Routing>): Promise<Routing | null> {
    const routings = await this.getRoutings();
    const index = routings.findIndex(r => r.id === id);

    if (index === -1) return null;

    routings[index] = {
      ...routings[index],
      ...updates,
      updatedAt: new Date().toISOString()
    };

    await this.saveToStore('routings', routings);
    return routings[index];
  }

  async deleteRouting(id: string): Promise<boolean> {
    const routings = await this.getRoutings();
    const index = routings.findIndex(r => r.id === id);

    if (index === -1) return false;

    routings.splice(index, 1);
    await this.saveToStore('routings', routings);
    return true;
  }
  // --- 实用方法 ---

  // 数据验证
  async validateEquipment(equipment: Partial<Equipment>): Promise<ValidationResult> {
    const errors: any[] = [];
    const warnings: any[] = [];

    if (!equipment.name?.trim()) {
      errors.push({ field: 'name', message: '设备名称不能为空', code: 'REQUIRED' });
    }

    if (!equipment.model?.trim()) {
      errors.push({ field: 'model', message: '设备型号不能为空', code: 'REQUIRED' });
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  async validateMaterial(material: Partial<Material>): Promise<ValidationResult> {
    const errors: any[] = [];
    const warnings: any[] = [];

    if (!material.name?.trim()) {
      errors.push({ field: 'name', message: '物料名称不能为空', code: 'REQUIRED' });
    }

    if (!material.type) {
      errors.push({ field: 'type', message: '物料类型不能为空', code: 'REQUIRED' });
    }

    if (!material.baseUnit) {
      errors.push({ field: 'baseUnit', message: '基本单位不能为空', code: 'REQUIRED' });
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  // 批量操作
  async batchCreateEquipments(equipments: Omit<Equipment, 'id' | 'createdAt' | 'updatedAt'>[]): Promise<BatchOperationResult<Equipment>> {
    const results: any[] = [];
    let processedCount = 0;
    let errorCount = 0;

    for (const equipment of equipments) {
      try {
        const validation = await this.validateEquipment(equipment);
        if (!validation.isValid) {
          results.push({
            item: equipment,
            success: false,
            error: validation.errors.map(e => e.message).join(', ')
          });
          errorCount++;
          continue;
        }

        const created = await this.createEquipment(equipment);
        results.push({
          item: created,
          success: true
        });
        processedCount++;
      } catch (error) {
        results.push({
          item: equipment,
          success: false,
          error: error instanceof Error ? error.message : '未知错误'
        });
        errorCount++;
      }
    }

    return {
      success: errorCount === 0,
      processedCount,
      errorCount,
      results
    };
  }

  // 搜索和过滤
  async searchEquipments(query: string): Promise<Equipment[]> {
    const equipments = await this.getEquipments();
    const lowerQuery = query.toLowerCase();

    return equipments.filter(eq =>
      eq.name.toLowerCase().includes(lowerQuery) ||
      eq.model.toLowerCase().includes(lowerQuery) ||
      eq.id.toLowerCase().includes(lowerQuery)
    );
  }

  async searchMaterials(query: string): Promise<Material[]> {
    const materials = await this.getMaterials();
    const lowerQuery = query.toLowerCase();

    return materials.filter(m =>
      m.name.toLowerCase().includes(lowerQuery) ||
      m.id.toLowerCase().includes(lowerQuery) ||
      m.type.toLowerCase().includes(lowerQuery)
    );
  }

  // 关联数据查询
  async getEquipmentsByWorkCenter(workCenterId: string): Promise<Equipment[]> {
    const workCenter = await this.getWorkCenterById(workCenterId);
    if (!workCenter) return [];

    const equipments = await this.getEquipments();
    return equipments.filter(eq => workCenter.equipmentIds.includes(eq.id));
  }

  async getRoutingsByProductFamily(productFamilyId: string): Promise<Routing[]> {
    const routings = await this.getRoutings();
    return routings.filter(r => r.productFamilyId === productFamilyId);
  }
}

export const masterDataService = new MasterDataService();

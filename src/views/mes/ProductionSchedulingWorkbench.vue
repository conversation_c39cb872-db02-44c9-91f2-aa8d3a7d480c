<template>
  <div class="container mx-auto p-6 space-y-6">
    <!-- 页面标题 -->
    <div>
      <h1 class="text-3xl font-bold text-gray-900">排产规划工作台</h1>
      <p class="text-gray-600 mt-1">在这里进行生产调度决策，平衡交期、成本和产能。</p>
    </div>

    <!-- 工作台布局 -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 h-[calc(100vh-200px)]">
      <!-- 左侧：待规划工单明细池 -->
      <div class="lg:col-span-1 bg-white rounded-lg border overflow-hidden flex flex-col">
        <div class="p-4 border-b">
          <h2 class="text-lg font-semibold">待规划明细池</h2>
          <p class="text-sm text-gray-500">所有已发布工单的明细项</p>
        </div>
        <div class="p-4 flex-grow overflow-y-auto">
          <p class="text-gray-500">这里将显示待规划的工单明细列表...</p>
          <!-- 待办项列表将在这里渲染 -->
        </div>
      </div>

      <!-- 右侧：排产计划看板 -->
      <div class="lg:col-span-2 bg-white rounded-lg border overflow-hidden flex flex-col">
        <div class="p-4 border-b">
          <h2 class="text-lg font-semibold">排产计划看板</h2>
          <p class="text-sm text-gray-500">将明细拖拽到这里以安排生产</p>
        </div>
        <div class="p-4 flex-grow overflow-y-auto">
          <p class="text-gray-500">这里将显示甘特图或看板视图...</p>
          <!-- 甘特图或看板将在这里渲染 -->
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';

onMounted(() => {
  console.log('排产规划工作台已加载');
});
</script>

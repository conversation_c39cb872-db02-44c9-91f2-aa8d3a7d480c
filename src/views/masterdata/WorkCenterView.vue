<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useMasterDataStore } from '@/stores/masterDataStore';
import type { WorkCenter } from '@/types/masterdata';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Plus, Search, Edit, Trash2, Factory } from 'lucide-vue-next';
import { toast } from 'vue-sonner';

const masterDataStore = useMasterDataStore();

// Reactive state
const searchQuery = ref('');
const isCreateDialogOpen = ref(false);
const isEditDialogOpen = ref(false);
const editingWorkCenter = ref<WorkCenter | null>(null);

// Form data
const formData = ref<Partial<WorkCenter>>({
  name: '',
  equipmentIds: [],
  calendarId: 'CAL-STANDARD',
  description: '',
  location: '',
  capacity: 0,
  efficiency: 95,
  costCenter: '',
  supervisor: ''
});

// Options
const availableCalendars = [
  { value: 'CAL-STANDARD', label: '标准工作日历' },
  { value: 'CAL-CONTINUOUS', label: '连续生产日历' }
];

// Computed
const filteredWorkCenters = computed(() => {
  if (!searchQuery.value) return masterDataStore.workCenters;

  const query = searchQuery.value.toLowerCase();
  return masterDataStore.workCenters.filter(wc =>
    wc.name.toLowerCase().includes(query) ||
    wc.id.toLowerCase().includes(query) ||
    wc.location?.toLowerCase().includes(query)
  );
});

const availableEquipments = computed(() => {
  return masterDataStore.equipments.filter(eq => eq.status !== 'fault');
});

// Methods
const resetForm = () => {
  formData.value = {
    name: '',
    equipmentIds: [],
    calendarId: 'CAL-STANDARD',
    description: '',
    location: '',
    capacity: 0,
    efficiency: 95,
    costCenter: '',
    supervisor: ''
  };
};

const openCreateDialog = () => {
  resetForm();
  isCreateDialogOpen.value = true;
};

const openEditDialog = (workCenter: WorkCenter) => {
  editingWorkCenter.value = workCenter;
  formData.value = { ...workCenter };
  isEditDialogOpen.value = true;
};

const handleCreate = async () => {
  try {
    if (!formData.value.name) {
      toast.error('请填写工作中心名称');
      return;
    }

    await masterDataStore.createWorkCenter(formData.value as Omit<WorkCenter, 'id' | 'createdAt' | 'updatedAt'>);
    toast.success('工作中心创建成功');
    isCreateDialogOpen.value = false;
    resetForm();
  } catch (error) {
    toast.error('工作中心创建失败');
  }
};

const handleUpdate = async () => {
  try {
    if (!editingWorkCenter.value || !formData.value.name) {
      toast.error('请填写工作中心名称');
      return;
    }

    await masterDataStore.updateWorkCenter(editingWorkCenter.value.id, formData.value);
    toast.success('工作中心更新成功');
    isEditDialogOpen.value = false;
    editingWorkCenter.value = null;
    resetForm();
  } catch (error) {
    toast.error('工作中心更新失败');
  }
};

const handleDelete = async (workCenter: WorkCenter) => {
  if (!confirm(`确定要删除工作中心 "${workCenter.name}" 吗？`)) {
    return;
  }

  try {
    await masterDataStore.deleteWorkCenter(workCenter.id);
    toast.success('工作中心删除成功');
  } catch (error) {
    toast.error('工作中心删除失败');
  }
};

const getEquipmentNames = (equipmentIds: string[]) => {
  return equipmentIds
    .map(id => masterDataStore.equipments.find(eq => eq.id === id)?.name)
    .filter(Boolean)
    .join(', ');
};

// Lifecycle
onMounted(() => {
  masterDataStore.fetchEquipments();
  masterDataStore.fetchWorkCenters();
});
</script>

<template>
  <div class="p-6 space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold tracking-tight">工作中心管理</h1>
        <p class="text-muted-foreground">管理工作中心配置和产能日历</p>
      </div>

      <Button @click="openCreateDialog" class="gap-2">
        <Plus class="h-4 w-4" />
        新增工作中心
      </Button>
    </div>

    <!-- Search and Filters -->
    <Card>
      <CardHeader>
        <CardTitle class="text-lg">搜索和筛选</CardTitle>
      </CardHeader>
      <CardContent>
        <div class="flex gap-4">
          <div class="flex-1">
            <div class="relative">
              <Search class="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                v-model="searchQuery"
                placeholder="搜索工作中心名称、编号或位置..."
                class="pl-10"
              />
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Work Centers Table -->
    <Card>
      <CardHeader>
        <CardTitle class="text-lg">工作中心列表</CardTitle>
        <CardDescription>
          共 {{ filteredWorkCenters.length }} 个工作中心
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div class="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>工作中心编号</TableHead>
                <TableHead>工作中心名称</TableHead>
                <TableHead>关联设备</TableHead>
                <TableHead>位置</TableHead>
                <TableHead>产能</TableHead>
                <TableHead>效率</TableHead>
                <TableHead>负责人</TableHead>
                <TableHead class="text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow v-if="masterDataStore.loading">
                <TableCell colspan="8" class="text-center py-8">
                  <div class="flex items-center justify-center gap-2">
                    <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
                    加载中...
                  </div>
                </TableCell>
              </TableRow>
              <TableRow v-else-if="filteredWorkCenters.length === 0">
                <TableCell colspan="8" class="text-center py-8 text-muted-foreground">
                  暂无工作中心数据
                </TableCell>
              </TableRow>
              <TableRow v-else v-for="workCenter in filteredWorkCenters" :key="workCenter.id">
                <TableCell class="font-medium">{{ workCenter.id }}</TableCell>
                <TableCell>{{ workCenter.name }}</TableCell>
                <TableCell>
                  <div class="max-w-[200px] truncate" :title="getEquipmentNames(workCenter.equipmentIds)">
                    {{ getEquipmentNames(workCenter.equipmentIds) || '无关联设备' }}
                  </div>
                </TableCell>
                <TableCell>{{ workCenter.location || '-' }}</TableCell>
                <TableCell>{{ workCenter.capacity || '-' }}</TableCell>
                <TableCell>
                  <span v-if="workCenter.efficiency">{{ workCenter.efficiency }}%</span>
                  <span v-else class="text-muted-foreground">-</span>
                </TableCell>
                <TableCell>{{ workCenter.supervisor || '-' }}</TableCell>
                <TableCell class="text-right">
                  <div class="flex items-center justify-end gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      @click="openEditDialog(workCenter)"
                      class="h-8 w-8 p-0"
                    >
                      <Edit class="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      @click="handleDelete(workCenter)"
                      class="h-8 w-8 p-0 text-destructive hover:text-destructive"
                    >
                      <Trash2 class="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>

    <!-- Create Work Center Dialog -->
    <Dialog v-model:open="isCreateDialogOpen">
      <DialogContent class="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>新增工作中心</DialogTitle>
        </DialogHeader>
        <div class="grid gap-4 py-4">
          <div class="grid grid-cols-2 gap-4">
            <div class="space-y-2">
              <Label for="name">工作中心名称 *</Label>
              <Input
                id="name"
                v-model="formData.name"
                placeholder="请输入工作中心名称"
              />
            </div>
            <div class="space-y-2">
              <Label for="location">位置</Label>
              <Input
                id="location"
                v-model="formData.location"
                placeholder="请输入位置"
              />
            </div>
          </div>

          <div class="grid grid-cols-2 gap-4">
            <div class="space-y-2">
              <Label for="capacity">产能</Label>
              <Input
                id="capacity"
                v-model.number="formData.capacity"
                type="number"
                placeholder="0"
              />
            </div>
            <div class="space-y-2">
              <Label for="efficiency">效率 (%)</Label>
              <Input
                id="efficiency"
                v-model.number="formData.efficiency"
                type="number"
                placeholder="95"
                min="0"
                max="100"
              />
            </div>
          </div>

          <div class="grid grid-cols-2 gap-4">
            <div class="space-y-2">
              <Label for="costCenter">成本中心</Label>
              <Input
                id="costCenter"
                v-model="formData.costCenter"
                placeholder="请输入成本中心"
              />
            </div>
            <div class="space-y-2">
              <Label for="supervisor">负责人</Label>
              <Input
                id="supervisor"
                v-model="formData.supervisor"
                placeholder="请输入负责人"
              />
            </div>
          </div>

          <div class="space-y-2">
            <Label for="calendarId">产能日历</Label>
            <Select v-model="formData.calendarId">
              <SelectTrigger>
                <SelectValue placeholder="选择产能日历" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem v-for="option in availableCalendars" :key="option.value" :value="option.value">
                  {{ option.label }}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div class="space-y-2">
            <Label for="description">描述</Label>
            <Textarea
              id="description"
              v-model="formData.description"
              placeholder="请输入工作中心描述"
              rows="3"
            />
          </div>
        </div>
        <div class="flex justify-end gap-2">
          <Button variant="outline" @click="isCreateDialogOpen = false">
            取消
          </Button>
          <Button @click="handleCreate" :disabled="masterDataStore.loading">
            创建
          </Button>
        </div>
      </DialogContent>
    </Dialog>

    <!-- Edit Work Center Dialog -->
    <Dialog v-model:open="isEditDialogOpen">
      <DialogContent class="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>编辑工作中心</DialogTitle>
        </DialogHeader>
        <div class="grid gap-4 py-4">
          <div class="grid grid-cols-2 gap-4">
            <div class="space-y-2">
              <Label for="edit-name">工作中心名称 *</Label>
              <Input
                id="edit-name"
                v-model="formData.name"
                placeholder="请输入工作中心名称"
              />
            </div>
            <div class="space-y-2">
              <Label for="edit-location">位置</Label>
              <Input
                id="edit-location"
                v-model="formData.location"
                placeholder="请输入位置"
              />
            </div>
          </div>

          <div class="grid grid-cols-2 gap-4">
            <div class="space-y-2">
              <Label for="edit-capacity">产能</Label>
              <Input
                id="edit-capacity"
                v-model.number="formData.capacity"
                type="number"
                placeholder="0"
              />
            </div>
            <div class="space-y-2">
              <Label for="edit-efficiency">效率 (%)</Label>
              <Input
                id="edit-efficiency"
                v-model.number="formData.efficiency"
                type="number"
                placeholder="95"
                min="0"
                max="100"
              />
            </div>
          </div>

          <div class="grid grid-cols-2 gap-4">
            <div class="space-y-2">
              <Label for="edit-costCenter">成本中心</Label>
              <Input
                id="edit-costCenter"
                v-model="formData.costCenter"
                placeholder="请输入成本中心"
              />
            </div>
            <div class="space-y-2">
              <Label for="edit-supervisor">负责人</Label>
              <Input
                id="edit-supervisor"
                v-model="formData.supervisor"
                placeholder="请输入负责人"
              />
            </div>
          </div>

          <div class="space-y-2">
            <Label for="edit-calendarId">产能日历</Label>
            <Select v-model="formData.calendarId">
              <SelectTrigger>
                <SelectValue placeholder="选择产能日历" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem v-for="option in availableCalendars" :key="option.value" :value="option.value">
                  {{ option.label }}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div class="space-y-2">
            <Label for="edit-description">描述</Label>
            <Textarea
              id="edit-description"
              v-model="formData.description"
              placeholder="请输入工作中心描述"
              rows="3"
            />
          </div>
        </div>
        <div class="flex justify-end gap-2">
          <Button variant="outline" @click="isEditDialogOpen = false">
            取消
          </Button>
          <Button @click="handleUpdate" :disabled="masterDataStore.loading">
            更新
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  </div>
</template>

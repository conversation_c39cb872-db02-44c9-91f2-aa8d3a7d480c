<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useMasterDataStore } from '@/stores/masterDataStore';
import type { Routing } from '@/types/masterdata';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Plus, Search, Edit, Trash2, Eye, Play, Archive } from 'lucide-vue-next';
import { toast } from 'vue-sonner';

const masterDataStore = useMasterDataStore();

// Reactive state
const searchQuery = ref('');
const isCreateDialogOpen = ref(false);
const isEditDialogOpen = ref(false);
const isViewerOpen = ref(false);
const editingRouting = ref<Routing | null>(null);
const viewingRouting = ref<Routing | null>(null);

// Form data
const formData = ref<Partial<Routing>>({
  name: '',
  productFamilyId: '',
  version: 1.0,
  status: 'draft',
  description: '',
  processSegmentIds: []
});

// Status options
const statusOptions = [
  { value: 'draft', label: '草稿', variant: 'secondary' },
  { value: 'active', label: '激活', variant: 'default' },
  { value: 'archived', label: '归档', variant: 'outline' }
] as const;

// Computed
const filteredRoutings = computed(() => {
  if (!searchQuery.value) return masterDataStore.routings;

  const query = searchQuery.value.toLowerCase();
  return masterDataStore.routings.filter(r =>
    r.name.toLowerCase().includes(query) ||
    r.id.toLowerCase().includes(query) ||
    r.version?.toString().toLowerCase().includes(query)
  );
});

const getStatusBadge = (status: string) => {
  const option = statusOptions.find(opt => opt.value === status);
  return option || { value: status, label: status, variant: 'secondary' };
};

// Methods
const resetForm = () => {
  formData.value = {
    name: '',
    productFamilyId: '',
    version: 1.0,
    status: 'draft',
    description: '',
    processSegmentIds: []
  };
};

const openCreateDialog = () => {
  resetForm();
  isCreateDialogOpen.value = true;
};

const openEditDialog = (routing: Routing) => {
  editingRouting.value = routing;
  formData.value = { ...routing };
  isEditDialogOpen.value = true;
};

const openViewer = (routing: Routing) => {
  viewingRouting.value = routing;
  isViewerOpen.value = true;
};

const handleCreate = async () => {
  try {
    if (!formData.value.name) {
      toast.error('请填写工艺路线名称');
      return;
    }

    await masterDataStore.createRouting(formData.value as Omit<Routing, 'id'>);
    toast.success('工艺路线创建成功');
    isCreateDialogOpen.value = false;
    resetForm();
  } catch (error) {
    toast.error('工艺路线创建失败');
  }
};

const handleUpdate = async () => {
  try {
    if (!editingRouting.value || !formData.value.name) {
      toast.error('请填写工艺路线名称');
      return;
    }

    await masterDataStore.updateRouting(editingRouting.value.id, formData.value);
    toast.success('工艺路线更新成功');
    isEditDialogOpen.value = false;
    editingRouting.value = null;
    resetForm();
  } catch (error) {
    toast.error('工艺路线更新失败');
  }
};

const handleDelete = async (routing: Routing) => {
  if (!confirm(`确定要删除工艺路线 "${routing.name}" 吗？`)) {
    return;
  }

  try {
    await masterDataStore.deleteRouting(routing.id);
    toast.success('工艺路线删除成功');
  } catch (error) {
    toast.error('工艺路线删除失败');
  }
};

const activateRouting = async (routing: Routing) => {
  try {
    await masterDataStore.updateRouting(routing.id, { status: 'active' });
    toast.success('工艺路线已激活');
  } catch (error) {
    toast.error('激活失败');
  }
};

const archiveRouting = async (routing: Routing) => {
  try {
    await masterDataStore.updateRouting(routing.id, { status: 'archived' });
    toast.success('工艺路线已归档');
  } catch (error) {
    toast.error('归档失败');
  }
};

const handleEditorSave = (data: { nodes: any[], edges: any[], version: string, status: string }) => {
  // Convert editor data to routing format
  const processSegmentIds = data.nodes
    .filter(node => node.data.type === 'process-segment')
    .map(node => node.data.properties.segmentId)
    .filter(Boolean);

  if (editingRouting.value) {
    // Update existing routing
    formData.value.processSegmentIds = processSegmentIds;
    formData.value.version = parseFloat(data.version);
    formData.value.status = data.status as any;
    handleUpdate();
  } else {
    // Create new routing
    formData.value.processSegmentIds = processSegmentIds;
    formData.value.version = parseFloat(data.version);
    formData.value.status = data.status as any;
    handleCreate();
  }

  isEditDialogOpen.value = false;
};

// Lifecycle
onMounted(() => {
  masterDataStore.fetchRoutings();
  masterDataStore.fetchProductFamilies();
});
</script>

<template>
  <div class="p-6 space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold tracking-tight">工艺路线管理</h1>
        <p class="text-muted-foreground">设计和管理完整的工艺路线，支持版本控制和生命周期管理</p>
      </div>

      <Button @click="openCreateDialog" class="gap-2">
        <Plus class="h-4 w-4" />
        新增工艺路线
      </Button>
    </div>

    <!-- Search and Filters -->
    <Card>
      <CardHeader>
        <CardTitle class="text-lg">搜索和筛选</CardTitle>
      </CardHeader>
      <CardContent>
        <div class="flex gap-4">
          <div class="flex-1">
            <div class="relative">
              <Search class="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                v-model="searchQuery"
                placeholder="搜索工艺路线名称、编号或版本..."
                class="pl-10"
              />
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Routings Table -->
    <Card>
      <CardHeader>
        <CardTitle class="text-lg">工艺路线列表</CardTitle>
        <CardDescription>
          共 {{ filteredRoutings.length }} 条工艺路线
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div class="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>路线编号</TableHead>
                <TableHead>路线名称</TableHead>
                <TableHead>产品族</TableHead>
                <TableHead>版本</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>预计周期</TableHead>
                <TableHead>描述</TableHead>
                <TableHead class="text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow v-if="masterDataStore.loading">
                <TableCell colspan="8" class="text-center py-8">
                  <div class="flex items-center justify-center gap-2">
                    <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
                    加载中...
                  </div>
                </TableCell>
              </TableRow>
              <TableRow v-else-if="filteredRoutings.length === 0">
                <TableCell colspan="8" class="text-center py-8 text-muted-foreground">
                  暂无工艺路线数据
                </TableCell>
              </TableRow>
              <TableRow v-else v-for="routing in filteredRoutings" :key="routing.id">
                <TableCell class="font-medium">{{ routing.id }}</TableCell>
                <TableCell>{{ routing.name }}</TableCell>
                <TableCell>
                  <Badge variant="outline">
                    {{ routing.productFamilyId }}
                  </Badge>
                </TableCell>
                <TableCell>v{{ routing.version }}</TableCell>
                <TableCell>
                  <Badge :variant="getStatusBadge(routing.status || 'draft').variant">
                    {{ getStatusBadge(routing.status || 'draft').label }}
                  </Badge>
                </TableCell>
                <TableCell>
                  <span v-if="routing.estimatedLeadTime">
                    {{ routing.estimatedLeadTime }}小时
                  </span>
                  <span v-else class="text-muted-foreground">-</span>
                </TableCell>
                <TableCell class="max-w-[200px] truncate">
                  {{ routing.description || '-' }}
                </TableCell>
                <TableCell class="text-right">
                  <div class="flex items-center justify-end gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      @click="openViewer(routing)"
                      class="h-8 w-8 p-0"
                    >
                      <Eye class="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      @click="openEditDialog(routing)"
                      class="h-8 w-8 p-0"
                    >
                      <Edit class="h-4 w-4" />
                    </Button>
                    <Button
                      v-if="routing.status === 'draft'"
                      variant="ghost"
                      size="sm"
                      @click="activateRouting(routing)"
                      class="h-8 w-8 p-0 text-green-600 hover:text-green-600"
                    >
                      <Play class="h-4 w-4" />
                    </Button>
                    <Button
                      v-if="routing.status === 'active'"
                      variant="ghost"
                      size="sm"
                      @click="archiveRouting(routing)"
                      class="h-8 w-8 p-0 text-orange-600 hover:text-orange-600"
                    >
                      <Archive class="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      @click="handleDelete(routing)"
                      class="h-8 w-8 p-0 text-destructive hover:text-destructive"
                    >
                      <Trash2 class="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  </div>
</template>

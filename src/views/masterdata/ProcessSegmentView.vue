<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useMasterDataStore } from '@/stores/masterDataStore';
import type { ProcessSegment } from '@/types/masterdata';
import ProcessSegmentEditor from '@/components/masterdata/ProcessSegmentEditor.vue';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Plus, Search, Edit, Trash2, Eye } from 'lucide-vue-next';
import { toast } from 'vue-sonner';

const masterDataStore = useMasterDataStore();

// Reactive state
const searchQuery = ref('');
const isEditorOpen = ref(false);
const isViewerOpen = ref(false);
const activeProcessSegment = ref<ProcessSegment | null>(null);

// Computed
const filteredProcessSegments = computed(() => {
  if (!searchQuery.value) return masterDataStore.processSegments;

  const query = searchQuery.value.toLowerCase();
  return masterDataStore.processSegments.filter(ps =>
    ps.name.toLowerCase().includes(query) ||
    ps.id.toLowerCase().includes(query)
  );
});

// Methods
const openCreateDialog = () => {
  activeProcessSegment.value = null;
  isEditorOpen.value = true;
};

const openEditDialog = (processSegment: ProcessSegment) => {
  activeProcessSegment.value = { ...processSegment }; // Use a copy to avoid reactivity issues
  isEditorOpen.value = true;
};

const openViewer = (processSegment: ProcessSegment) => {
  activeProcessSegment.value = processSegment;
  isViewerOpen.value = true;
};

const handleEditorSave = async (segment: ProcessSegment) => {
  try {
    if (masterDataStore.processSegments.some(ps => ps.id === segment.id)) {
      // Update existing
      await masterDataStore.updateProcessSegment(segment.id, segment);
      toast.success(`工艺段 "${segment.name}" 更新成功`);
    } else {
      // Create new
      await masterDataStore.createProcessSegment(segment);
      toast.success(`工艺段 "${segment.name}" 创建成功`);
    }
    isEditorOpen.value = false;
    activeProcessSegment.value = null;
  } catch (error) {
    toast.error('保存工艺段失败');
    console.error(error);
  }
};

const handleDelete = async (processSegment: ProcessSegment) => {
  if (!confirm(`确定要删除工艺段 "${processSegment.name}" 吗？`)) {
    return;
  }

  try {
    await masterDataStore.deleteProcessSegment(processSegment.id);
    toast.success('工艺段删除成功');
  } catch (error) {
    toast.error('工艺段删除失败');
  }
};

// Lifecycle
onMounted(async () => {
  await masterDataStore.fetchProcessSegments();
});
</script>

<template>
  <div class="p-6 space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold tracking-tight">工艺段管理</h1>
        <p class="text-muted-foreground">通过可视化编辑器，将标准工序组合成可复用的工艺段。</p>
      </div>

      <Button @click="openCreateDialog" class="gap-2">
        <Plus class="h-4 w-4" />
        新增工艺段
      </Button>
    </div>

    <!-- Search -->
    <Card>
      <CardContent class="pt-6">
        <div class="relative">
          <Search class="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
          <Input
            v-model="searchQuery"
            placeholder="搜索工艺段名称或编号..."
            class="pl-10"
          />
        </div>
      </CardContent>
    </Card>

    <!-- Process Segments Table -->
    <Card>
      <CardHeader>
        <CardTitle>工艺段列表</CardTitle>
        <CardDescription>
          共 {{ filteredProcessSegments.length }} 个工艺段
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div class="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>编号</TableHead>
                <TableHead>名称</TableHead>
                <TableHead>节点数</TableHead>
                <TableHead>描述</TableHead>
                <TableHead class="text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow v-if="masterDataStore.loading">
                <TableCell colspan="5" class="text-center py-8">加载中...</TableCell>
              </TableRow>
              <TableRow v-else-if="filteredProcessSegments.length === 0">
                <TableCell colspan="5" class="text-center py-8 text-muted-foreground">
                  暂无工艺段数据
                </TableCell>
              </TableRow>
              <TableRow v-else v-for="segment in filteredProcessSegments" :key="segment.id">
                <TableCell class="font-mono text-sm">{{ segment.id }}</TableCell>
                <TableCell class="font-medium">{{ segment.name }}</TableCell>
                <TableCell>
                  <Badge variant="outline">
                    {{ segment.nodes.length }} 个节点
                  </Badge>
                </TableCell>
                <TableCell class="max-w-[300px] truncate text-muted-foreground">
                  {{ segment.description || '-' }}
                </TableCell>
                <TableCell class="text-right">
                  <div class="flex items-center justify-end gap-2">
                    <Button variant="ghost" size="icon" @click="openViewer(segment)">
                      <Eye class="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="icon" @click="openEditDialog(segment)">
                      <Edit class="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="icon" @click="handleDelete(segment)" class="text-destructive hover:text-destructive">
                      <Trash2 class="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>

    <!-- Editor Dialog (for Create and Edit) -->
    <Dialog v-model:open="isEditorOpen">
      <DialogContent class="max-w-7xl w-full h-[90vh]">
        <DialogHeader>
          <DialogTitle>{{ activeProcessSegment ? '编辑' : '新增' }}工艺段</DialogTitle>
          <DialogDescription>
            拖拽节点、连接流程，定义工艺段。
          </DialogDescription>
        </DialogHeader>
        <div class="h-[calc(90vh-100px)]">
          <ProcessSegmentEditor
            :process-segment="activeProcessSegment"
            @save="handleEditorSave"
            @cancel="isEditorOpen = false"
          />
        </div>
      </DialogContent>
    </Dialog>

    <!-- Viewer Dialog -->
    <Dialog v-model:open="isViewerOpen">
      <DialogContent class="max-w-7xl w-full h-[90vh]">
        <DialogHeader>
          <DialogTitle>查看工艺段: {{ activeProcessSegment?.name }}</DialogTitle>
        </DialogHeader>
        <div class="h-[calc(90vh-100px)]">
          <ProcessSegmentEditor
            :process-segment="activeProcessSegment"
            :readonly="true"
            @cancel="isViewerOpen = false"
          />
        </div>
      </DialogContent>
    </Dialog>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useMasterDataStore } from '@/stores/masterDataStore';
import type { ProcessSegment } from '@/types/masterdata';
import ProcessSegmentEditor from '@/components/masterdata/ProcessSegmentEditor.vue';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Plus, Search, Edit, Trash2, Eye, GitBranch } from 'lucide-vue-next';
import { toast } from 'vue-sonner';

const masterDataStore = useMasterDataStore();

// Reactive state
const searchQuery = ref('');
const isCreateDialogOpen = ref(false);
const isEditDialogOpen = ref(false);
const isViewerOpen = ref(false);
const editingProcessSegment = ref<ProcessSegment | null>(null);
const viewingProcessSegment = ref<ProcessSegment | null>(null);

// Form data
const formData = ref<Partial<ProcessSegment>>({
  name: '',
  description: '',
  processStepIds: [],
  wipBufferIds: [],
  inputBufferId: '',
  outputBufferId: ''
});

// Computed
const filteredProcessSegments = computed(() => {
  if (!searchQuery.value) return masterDataStore.processSegments;

  const query = searchQuery.value.toLowerCase();
  return masterDataStore.processSegments.filter(ps =>
    ps.name.toLowerCase().includes(query) ||
    ps.id.toLowerCase().includes(query)
  );
});

// Methods
const resetForm = () => {
  formData.value = {
    name: '',
    description: '',
    processStepIds: [],
    wipBufferIds: [],
    inputBufferId: '',
    outputBufferId: ''
  };
};

const openCreateDialog = () => {
  resetForm();
  isCreateDialogOpen.value = true;
};

const openEditDialog = (processSegment: ProcessSegment) => {
  editingProcessSegment.value = processSegment;
  formData.value = { ...processSegment };
  isEditDialogOpen.value = true;
};

const openViewer = (processSegment: ProcessSegment) => {
  viewingProcessSegment.value = processSegment;
  isViewerOpen.value = true;
};

const handleCreate = async () => {
  try {
    if (!formData.value.name) {
      toast.error('请填写工艺段名称');
      return;
    }

    await masterDataStore.createProcessSegment(formData.value as Omit<ProcessSegment, 'id'>);
    toast.success('工艺段创建成功');
    isCreateDialogOpen.value = false;
    resetForm();
  } catch (error) {
    toast.error('工艺段创建失败');
  }
};

const handleUpdate = async () => {
  try {
    if (!editingProcessSegment.value || !formData.value.name) {
      toast.error('请填写工艺段名称');
      return;
    }

    await masterDataStore.updateProcessSegment(editingProcessSegment.value.id, formData.value);
    toast.success('工艺段更新成功');
    isEditDialogOpen.value = false;
    editingProcessSegment.value = null;
    resetForm();
  } catch (error) {
    toast.error('工艺段更新失败');
  }
};

const handleDelete = async (processSegment: ProcessSegment) => {
  if (!confirm(`确定要删除工艺段 "${processSegment.name}" 吗？`)) {
    return;
  }

  try {
    await masterDataStore.deleteProcessSegment(processSegment.id);
    toast.success('工艺段删除成功');
  } catch (error) {
    toast.error('工艺段删除失败');
  }
};

const handleEditorSave = (data: { nodes: any[], edges: any[] }) => {
  // Convert editor data to process segment format
  const processStepIds = data.nodes
    .filter(node => node.data.type === 'process-step')
    .map(node => node.id);

  const wipBufferIds = data.nodes
    .filter(node => node.data.type === 'wip-buffer')
    .map(node => node.id);

  if (editingProcessSegment.value) {
    // Update existing process segment
    formData.value.processStepIds = processStepIds;
    formData.value.wipBufferIds = wipBufferIds;
    handleUpdate();
  } else {
    // Create new process segment
    formData.value.processStepIds = processStepIds;
    formData.value.wipBufferIds = wipBufferIds;
    handleCreate();
  }

  isEditDialogOpen.value = false;
};

// Lifecycle
onMounted(() => {
  masterDataStore.fetchProcessSegments();
});
</script>

<template>
  <div class="p-6 space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold tracking-tight">工艺段管理</h1>
        <p class="text-muted-foreground">设计和管理工艺段流程，配置工序和缓冲区</p>
      </div>

      <Button @click="openCreateDialog" class="gap-2">
        <Plus class="h-4 w-4" />
        新增工艺段
      </Button>
    </div>

    <!-- Search and Filters -->
    <Card>
      <CardHeader>
        <CardTitle class="text-lg">搜索和筛选</CardTitle>
      </CardHeader>
      <CardContent>
        <div class="flex gap-4">
          <div class="flex-1">
            <div class="relative">
              <Search class="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                v-model="searchQuery"
                placeholder="搜索工艺段名称或编号..."
                class="pl-10"
              />
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Process Segments Table -->
    <Card>
      <CardHeader>
        <CardTitle class="text-lg">工艺段列表</CardTitle>
        <CardDescription>
          共 {{ filteredProcessSegments.length }} 个工艺段
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div class="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>工艺段编号</TableHead>
                <TableHead>工艺段名称</TableHead>
                <TableHead>包含工序</TableHead>
                <TableHead>缓冲区</TableHead>
                <TableHead>描述</TableHead>
                <TableHead class="text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow v-if="masterDataStore.loading">
                <TableCell colspan="6" class="text-center py-8">
                  <div class="flex items-center justify-center gap-2">
                    <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
                    加载中...
                  </div>
                </TableCell>
              </TableRow>
              <TableRow v-else-if="filteredProcessSegments.length === 0">
                <TableCell colspan="6" class="text-center py-8 text-muted-foreground">
                  暂无工艺段数据
                </TableCell>
              </TableRow>
              <TableRow v-else v-for="processSegment in filteredProcessSegments" :key="processSegment.id">
                <TableCell class="font-medium">{{ processSegment.id }}</TableCell>
                <TableCell>{{ processSegment.name }}</TableCell>
                <TableCell>
                  <Badge variant="outline">
                    {{ processSegment.processStepIds?.length || processSegment.nodes?.filter(n => n.type === 'ProcessStep').length || 0 }} 个工序
                  </Badge>
                </TableCell>
                <TableCell>
                  <Badge variant="secondary">
                    {{ processSegment.wipBufferIds?.length || processSegment.nodes?.filter(n => n.type === 'WipBuffer').length || 0 }} 个缓冲区
                  </Badge>
                </TableCell>
                <TableCell class="max-w-[200px] truncate">
                  {{ processSegment.description || '-' }}
                </TableCell>
                <TableCell class="text-right">
                  <div class="flex items-center justify-end gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      @click="openViewer(processSegment)"
                      class="h-8 w-8 p-0"
                    >
                      <Eye class="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      @click="openEditDialog(processSegment)"
                      class="h-8 w-8 p-0"
                    >
                      <Edit class="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      @click="handleDelete(processSegment)"
                      class="h-8 w-8 p-0 text-destructive hover:text-destructive"
                    >
                      <Trash2 class="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>

    <!-- Create Process Segment Dialog -->
    <Dialog v-model:open="isCreateDialogOpen">
      <DialogContent class="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>新增工艺段</DialogTitle>
        </DialogHeader>
        <div class="grid gap-4 py-4">
          <div class="space-y-2">
            <Label for="name">工艺段名称 *</Label>
            <Input
              id="name"
              v-model="formData.name"
              placeholder="请输入工艺段名称"
            />
          </div>

          <div class="space-y-2">
            <Label for="description">工艺段描述</Label>
            <Input
              id="description"
              v-model="formData.description"
              placeholder="请输入工艺段描述"
            />
          </div>
        </div>
        <div class="flex justify-end gap-2">
          <Button variant="outline" @click="isCreateDialogOpen = false">
            取消
          </Button>
          <Button @click="handleCreate" :disabled="masterDataStore.loading">
            创建
          </Button>
        </div>
      </DialogContent>
    </Dialog>

    <!-- Edit Process Segment Dialog -->
    <Dialog v-model:open="isEditDialogOpen">
      <DialogContent class="sm:max-w-[1200px] max-h-[90vh] w-[1200px]">
        <DialogHeader>
          <DialogTitle>编辑工艺段 - {{ editingProcessSegment?.name }}</DialogTitle>
        </DialogHeader>
        <div class="h-[70vh]">
          <ProcessSegmentEditor
            v-if="isEditDialogOpen"
            :process-segment-id="editingProcessSegment?.id"
            @save="handleEditorSave"
            @cancel="isEditDialogOpen = false"
          />
        </div>
      </DialogContent>
    </Dialog>

    <!-- View Process Segment Dialog -->
    <Dialog v-model:open="isViewerOpen">
      <DialogContent class="sm:max-w-[1200px] max-h-[90vh] w-[1200px]">
        <DialogHeader>
          <DialogTitle>查看工艺段 - {{ viewingProcessSegment?.name }}</DialogTitle>
        </DialogHeader>
        <div class="h-[70vh]">
          <ProcessSegmentEditor
            v-if="isViewerOpen"
            :process-segment-id="viewingProcessSegment?.id"
            :readonly="true"
            @cancel="isViewerOpen = false"
          />
        </div>
      </DialogContent>
    </Dialog>
  </div>
</template>

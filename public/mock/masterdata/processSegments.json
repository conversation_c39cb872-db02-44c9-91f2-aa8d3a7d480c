[{"id": "SEG-COLD", "name": "冷加工段", "description": "玻璃冷加工工艺段，包含切割、磨边、清洗等工序", "nodes": [{"id": "node_cut_001", "type": "process-step", "position": {"x": 100, "y": 100}, "data": {"label": "玻璃切割", "type": "process-step", "properties": {"processingTime": 5, "setupTime": 2, "workCenter": "WC-CUT", "description": "使用自动切割机进行玻璃切割"}}}, {"id": "node_buffer_001", "type": "wip-buffer", "position": {"x": 300, "y": 100}, "data": {"label": "切割缓冲区", "type": "wip-buffer", "properties": {"capacity": 50, "unit": "piece", "bufferType": "fifo", "description": "切割后的玻璃临时存放区"}}}], "edges": [{"id": "edge_001", "source": "node_cut_001", "target": "node_buffer_001", "type": "default", "data": {"label": "传输", "condition": ""}}], "processStepIds": ["PS-CUT-001", "PS-EDGE-001"], "wipBufferIds": ["WIP-CUT-<PERSON><PERSON><PERSON>ER"], "inputBufferId": "WIP-RAW-INPUT", "outputBufferId": "WIP-COLD-OUTPUT"}]